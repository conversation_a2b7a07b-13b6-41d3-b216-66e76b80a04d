/**
 * Registration Page - Multi-Step Shipping Account Setup
 *
 * Comprehensive registration flow for LibyanoEx shipping forwarding portal
 *
 * Features:
 * - Multi-step registration process (Account → Address → Plan → Payment)
 * - Form validation with Zod schemas
 * - Subscription plan selection
 * - Address validation for shipping
 * - Progress indicator
 * - Responsive design optimized for shipping company needs
 * - Integration with Supabase authentication
 *
 * Steps:
 * 1. Account Details - Basic user information and credentials
 * 2. Shipping Address - Primary shipping address setup
 * 3. Subscription Plan - Choose shipping plan and features
 * 4. Payment Setup - Payment method configuration
 * 5. Confirmation - Email verification and welcome
 */

'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { signUpWithEmail } from '@/lib/auth'
import {
  Package,
  Eye,
  EyeOff,
  Loader2,
  ChevronLeft,
  ChevronRight,
  Check,
  User,
  MapPin,
  CreditCard,
  Crown,
  Truck,
  Shield,
  Clock
} from 'lucide-react'

// Registration step types
type RegistrationStep = 'account' | 'address' | 'plan' | 'payment' | 'confirmation'

// Subscription plan types
type SubscriptionPlan = 'basic' | 'standard' | 'premium'

// Form schemas for each step
const accountSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Please enter a valid phone number'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
  agreeToTerms: z.boolean().refine(val => val === true, {
    message: 'You must agree to the terms and conditions'
  })
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

const addressSchema = z.object({
  street: z.string().min(5, 'Street address is required'),
  city: z.string().min(2, 'City is required'),
  state: z.string().min(2, 'State is required'),
  zipCode: z.string().min(5, 'ZIP code is required'),
  country: z.string().min(2, 'Country is required'),
  isBusinessAddress: z.boolean().optional(),
  companyName: z.string().optional(),
})

const planSchema = z.object({
  selectedPlan: z.enum(['basic', 'standard', 'premium']),
  addOns: z.array(z.string()).optional(),
})

// Temporary payment schema for development (all fields optional since payment is not implemented yet)
const paymentSchema = z.object({
  cardNumber: z.string().optional(),
  expiryDate: z.string().optional(),
  cvv: z.string().optional(),
  cardholderName: z.string().optional(),
  billingAddress: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
  }).optional(),
  sameAsShipping: z.boolean().optional(),
})

type AccountForm = z.infer<typeof accountSchema>
type AddressForm = z.infer<typeof addressSchema>
type PlanForm = z.infer<typeof planSchema>
type PaymentForm = z.infer<typeof paymentSchema>

// Subscription plan configurations
const subscriptionPlans = {
  basic: {
    name: 'Basic',
    price: 9.99,
    description: 'Perfect for occasional shippers',
    features: [
      'Up to 10 packages per month',
      'Basic package consolidation',
      'Standard shipping rates',
      'Email support',
      '30-day free storage'
    ],
    icon: Package,
    color: 'blue'
  },
  standard: {
    name: 'Standard',
    price: 19.99,
    description: 'Great for regular shippers',
    features: [
      'Up to 50 packages per month',
      'Advanced package consolidation',
      'Discounted shipping rates',
      'Priority email support',
      '60-day free storage',
      'Package photos included'
    ],
    icon: Truck,
    color: 'purple',
    popular: true
  },
  premium: {
    name: 'Premium',
    price: 39.99,
    description: 'For high-volume shippers',
    features: [
      'Unlimited packages',
      'Premium consolidation service',
      'Best shipping rates',
      'Phone & email support',
      '90-day free storage',
      'Package photos & inspection',
      'Personal shopper service'
    ],
    icon: Crown,
    color: 'gold'
  }
} as const

export default function RegisterPage() {
  // State management
  const [currentStep, setCurrentStep] = useState<RegistrationStep>('account')
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const router = useRouter()

  // Form data storage
  const [registrationData, setRegistrationData] = useState({
    account: {} as AccountForm,
    address: {} as AddressForm,
    plan: { selectedPlan: 'standard' as SubscriptionPlan },
    payment: {} as PaymentForm,
  })

  // Form hooks for each step
  const accountForm = useForm<AccountForm>({
    resolver: zodResolver(accountSchema),
    defaultValues: registrationData.account
  })

  const addressForm = useForm<AddressForm>({
    resolver: zodResolver(addressSchema),
    defaultValues: registrationData.address
  })

  const planForm = useForm<PlanForm>({
    resolver: zodResolver(planSchema),
    defaultValues: registrationData.plan
  })

  const paymentForm = useForm<PaymentForm>({
    resolver: zodResolver(paymentSchema),
    defaultValues: registrationData.payment
  })

  // Step navigation
  const steps: { key: RegistrationStep; title: string; icon: any }[] = [
    { key: 'account', title: 'Account', icon: User },
    { key: 'address', title: 'Address', icon: MapPin },
    { key: 'plan', title: 'Plan', icon: Package },
    { key: 'payment', title: 'Payment', icon: CreditCard },
    { key: 'confirmation', title: 'Confirm', icon: Check },
  ]

  const currentStepIndex = steps.findIndex(step => step.key === currentStep)
  const isLastStep = currentStepIndex === steps.length - 1
  const isFirstStep = currentStepIndex === 0

  /**
   * Handles moving to the next step with validation
   */
  const handleNextStep = async () => {
    let isValid = false

    switch (currentStep) {
      case 'account':
        isValid = await accountForm.trigger()
        if (isValid) {
          setRegistrationData(prev => ({
            ...prev,
            account: accountForm.getValues()
          }))
          setCurrentStep('address')
        }
        break
      case 'address':
        isValid = await addressForm.trigger()
        if (isValid) {
          setRegistrationData(prev => ({
            ...prev,
            address: addressForm.getValues()
          }))
          setCurrentStep('plan')
        }
        break
      case 'plan':
        isValid = await planForm.trigger()
        if (isValid) {
          setRegistrationData(prev => ({
            ...prev,
            plan: planForm.getValues()
          }))
          setCurrentStep('payment')
        }
        break
      case 'payment':
        console.log('💳 Validating payment form...')
        isValid = await paymentForm.trigger()
        console.log('Payment form validation result:', isValid)

        if (isValid) {
          console.log('✅ Payment form is valid, proceeding with registration...')
          setRegistrationData(prev => ({
            ...prev,
            payment: paymentForm.getValues()
          }))
          await handleSubmitRegistration()
        } else {
          console.log('❌ Payment form validation failed')
          const errors = paymentForm.formState.errors
          console.log('Payment form errors:', errors)
        }
        break
    }
  }

  /**
   * Handles moving to the previous step
   */
  const handlePreviousStep = () => {
    const stepOrder: RegistrationStep[] = ['account', 'address', 'plan', 'payment', 'confirmation']
    const currentIndex = stepOrder.indexOf(currentStep)
    if (currentIndex > 0) {
      setCurrentStep(stepOrder[currentIndex - 1])
    }
  }

  /**
   * Handles the final registration submission
   */
  const handleSubmitRegistration = async () => {
    console.log('🚀 Starting registration submission...')
    console.log('Registration data:', registrationData)

    setIsLoading(true)
    setError(null)

    try {
      const { account, address, plan, payment } = registrationData

      console.log('📧 Creating user account for:', account.email)

      // Create user account
      await signUpWithEmail(
        account.email,
        account.password,
        `${account.firstName} ${account.lastName}`
      )

      console.log('✅ User account created successfully!')

      // TODO: Save additional registration data to database
      // - Address information
      // - Subscription plan
      // - Payment method

      setCurrentStep('confirmation')
      setSuccess(true)
      console.log('🎉 Registration completed successfully!')
    } catch (error) {
      console.error('❌ Registration error:', error)
      setError(error instanceof Error ? error.message : 'An error occurred during registration')
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Renders the progress indicator
   */
  const renderProgressIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {steps.map((step, index) => {
        const isActive = step.key === currentStep
        const isCompleted = index < currentStepIndex
        const StepIcon = step.icon

        return (
          <div key={step.key} className="flex items-center">
            <div className={`
              flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
              ${isActive
                ? 'border-blue-600 bg-blue-600 text-white'
                : isCompleted
                  ? 'border-green-600 bg-green-600 text-white'
                  : 'border-gray-300 bg-white text-gray-400'
              }
            `}>
              {isCompleted ? (
                <Check className="h-5 w-5" />
              ) : (
                <StepIcon className="h-5 w-5" />
              )}
            </div>
            <span className={`
              ml-2 text-sm font-medium
              ${isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'}
            `}>
              {step.title}
            </span>
            {index < steps.length - 1 && (
              <div className={`
                w-8 h-0.5 mx-4
                ${index < currentStepIndex ? 'bg-green-600' : 'bg-gray-300'}
              `} />
            )}
          </div>
        )
      })}
    </div>
  )

  /**
   * Renders the account details step
   */
  const renderAccountStep = () => (
    <form className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName">First Name</Label>
          <Input
            id="firstName"
            placeholder="Enter your first name"
            {...accountForm.register('firstName')}
            className={accountForm.formState.errors.firstName ? 'border-red-500' : ''}
          />
          {accountForm.formState.errors.firstName && (
            <p className="text-sm text-red-500">{accountForm.formState.errors.firstName.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="lastName">Last Name</Label>
          <Input
            id="lastName"
            placeholder="Enter your last name"
            {...accountForm.register('lastName')}
            className={accountForm.formState.errors.lastName ? 'border-red-500' : ''}
          />
          {accountForm.formState.errors.lastName && (
            <p className="text-sm text-red-500">{accountForm.formState.errors.lastName.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="email">Email Address</Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          {...accountForm.register('email')}
          className={accountForm.formState.errors.email ? 'border-red-500' : ''}
        />
        {accountForm.formState.errors.email && (
          <p className="text-sm text-red-500">{accountForm.formState.errors.email.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="phone">Phone Number</Label>
        <Input
          id="phone"
          type="tel"
          placeholder="+****************"
          {...accountForm.register('phone')}
          className={accountForm.formState.errors.phone ? 'border-red-500' : ''}
        />
        {accountForm.formState.errors.phone && (
          <p className="text-sm text-red-500">{accountForm.formState.errors.phone.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="password">Password</Label>
        <div className="relative">
          <Input
            id="password"
            type={showPassword ? 'text' : 'password'}
            placeholder="Create a secure password"
            {...accountForm.register('password')}
            className={accountForm.formState.errors.password ? 'border-red-500 pr-10' : 'pr-10'}
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        </div>
        {accountForm.formState.errors.password && (
          <p className="text-sm text-red-500">{accountForm.formState.errors.password.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="confirmPassword">Confirm Password</Label>
        <div className="relative">
          <Input
            id="confirmPassword"
            type={showConfirmPassword ? 'text' : 'password'}
            placeholder="Confirm your password"
            {...accountForm.register('confirmPassword')}
            className={accountForm.formState.errors.confirmPassword ? 'border-red-500 pr-10' : 'pr-10'}
          />
          <button
            type="button"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        </div>
        {accountForm.formState.errors.confirmPassword && (
          <p className="text-sm text-red-500">{accountForm.formState.errors.confirmPassword.message}</p>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="agreeToTerms"
          {...accountForm.register('agreeToTerms')}
        />
        <Label htmlFor="agreeToTerms" className="text-sm">
          I agree to the{' '}
          <a href="https://libyanoex.com/terms" className="text-blue-600 hover:text-blue-500">
            Terms of Service
          </a>{' '}
          and{' '}
          <a href="https://libyanoex.com/privacy" className="text-blue-600 hover:text-blue-500">
            Privacy Policy
          </a>
        </Label>
      </div>
      {accountForm.formState.errors.agreeToTerms && (
        <p className="text-sm text-red-500">{accountForm.formState.errors.agreeToTerms.message}</p>
      )}
    </form>
  )

  /**
   * Renders the address step
   */
  const renderAddressStep = () => (
    <form className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="street">Street Address</Label>
        <Input
          id="street"
          placeholder="123 Main Street"
          {...addressForm.register('street')}
          className={addressForm.formState.errors.street ? 'border-red-500' : ''}
        />
        {addressForm.formState.errors.street && (
          <p className="text-sm text-red-500">{addressForm.formState.errors.street.message}</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="city">City</Label>
          <Input
            id="city"
            placeholder="New York"
            {...addressForm.register('city')}
            className={addressForm.formState.errors.city ? 'border-red-500' : ''}
          />
          {addressForm.formState.errors.city && (
            <p className="text-sm text-red-500">{addressForm.formState.errors.city.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="state">State</Label>
          <Input
            id="state"
            placeholder="NY"
            {...addressForm.register('state')}
            className={addressForm.formState.errors.state ? 'border-red-500' : ''}
          />
          {addressForm.formState.errors.state && (
            <p className="text-sm text-red-500">{addressForm.formState.errors.state.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="zipCode">ZIP Code</Label>
          <Input
            id="zipCode"
            placeholder="10001"
            {...addressForm.register('zipCode')}
            className={addressForm.formState.errors.zipCode ? 'border-red-500' : ''}
          />
          {addressForm.formState.errors.zipCode && (
            <p className="text-sm text-red-500">{addressForm.formState.errors.zipCode.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="country">Country</Label>
          <Input
            id="country"
            placeholder="United States"
            {...addressForm.register('country')}
            className={addressForm.formState.errors.country ? 'border-red-500' : ''}
          />
          {addressForm.formState.errors.country && (
            <p className="text-sm text-red-500">{addressForm.formState.errors.country.message}</p>
          )}
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="isBusinessAddress"
          {...addressForm.register('isBusinessAddress')}
        />
        <Label htmlFor="isBusinessAddress" className="text-sm">
          This is a business address
        </Label>
      </div>

      {addressForm.watch('isBusinessAddress') && (
        <div className="space-y-2">
          <Label htmlFor="companyName">Company Name</Label>
          <Input
            id="companyName"
            placeholder="Your Company Name"
            {...addressForm.register('companyName')}
          />
        </div>
      )}
    </form>
  )

  /**
   * Renders the subscription plan selection step
   */
  const renderPlanStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Choose Your Shipping Plan
        </h3>
        <p className="text-gray-600 dark:text-gray-300">
          Select the plan that best fits your shipping needs
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {Object.entries(subscriptionPlans).map(([key, plan]) => {
          const PlanIcon = plan.icon
          const isSelected = planForm.watch('selectedPlan') === key

          return (
            <Card
              key={key}
              className={`relative cursor-pointer transition-all hover:shadow-md ${
                isSelected ? 'ring-2 ring-blue-600 border-blue-600' : ''
              }`}
              onClick={() => planForm.setValue('selectedPlan', key as SubscriptionPlan)}
            >
              {plan.popular && (
                <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-blue-600">
                  Most Popular
                </Badge>
              )}

              <CardHeader className="text-center pb-2">
                <div className="flex justify-center mb-2">
                  <PlanIcon className={`h-8 w-8 text-${plan.color}-600`} />
                </div>
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  ${plan.price}
                  <span className="text-sm font-normal text-gray-500">/month</span>
                </div>
                <CardDescription>{plan.description}</CardDescription>
              </CardHeader>

              <CardContent>
                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <Check className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )

  // Success/Confirmation step
  if (success && currentStep === 'confirmation') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <div className="flex items-center justify-center mb-4">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center">
                <Check className="h-8 w-8 text-white" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-center">Welcome to LibyanoEx!</CardTitle>
            <CardDescription className="text-center">
              Your account has been created successfully. Please check your email to verify your account.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                What's Next?
              </h4>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <li>• Check your email for verification link</li>
                <li>• Complete your profile setup</li>
                <li>• Start shipping with LibyanoEx</li>
              </ul>
            </div>
            <div className="text-center space-y-2">
              <Button asChild className="w-full">
                <Link href="/auth/login">
                  Continue to Sign In
                </Link>
              </Button>
              <Button variant="outline" asChild className="w-full">
                <Link href="/">
                  Back to Home
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2 mb-6">
            <Package className="h-8 w-8 text-blue-600" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">LibyanoEx</span>
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Create Your Shipping Account
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Join thousands of customers who trust LibyanoEx for their shipping needs
          </p>
        </div>

        {/* Progress Indicator */}
        {renderProgressIndicator()}

        {/* Main Content */}
        <Card className="w-full max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="text-xl">
              {currentStep === 'account' && 'Account Information'}
              {currentStep === 'address' && 'Shipping Address'}
              {currentStep === 'plan' && 'Subscription Plan'}
              {currentStep === 'payment' && 'Payment Information'}
            </CardTitle>
            <CardDescription>
              {currentStep === 'account' && 'Enter your personal details and create your account'}
              {currentStep === 'address' && 'Provide your primary shipping address'}
              {currentStep === 'plan' && 'Choose the plan that fits your shipping needs'}
              {currentStep === 'payment' && 'Set up your payment method for subscription'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Error Display */}
            {error && (
              <div className="mb-4 p-3 text-sm text-red-500 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                {error}
              </div>
            )}

            {/* Step Content */}
            <div className="mb-6">
              {currentStep === 'account' && renderAccountStep()}
              {currentStep === 'address' && renderAddressStep()}
              {currentStep === 'plan' && renderPlanStep()}
              {currentStep === 'payment' && (
                <div className="text-center py-8">
                  <CreditCard className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Payment Setup
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Payment integration will be implemented in the next phase
                  </p>
                  <p className="text-sm text-gray-500">
                    For now, you can complete registration and set up payment later
                  </p>
                </div>
              )}
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={handlePreviousStep}
                disabled={isFirstStep || isLoading}
                className="flex items-center space-x-2"
              >
                <ChevronLeft className="h-4 w-4" />
                <span>Previous</span>
              </Button>

              <Button
                type="button"
                onClick={handleNextStep}
                disabled={isLoading}
                className="flex items-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {currentStep === 'payment' ? 'Creating Account...' : 'Processing...'}
                  </>
                ) : (
                  <>
                    <span>
                      {currentStep === 'payment' ? 'Complete Registration' : 'Next'}
                    </span>
                    {currentStep !== 'payment' && <ChevronRight className="h-4 w-4" />}
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Footer Links */}
        <div className="mt-8 text-center text-sm text-gray-600 dark:text-gray-300">
          <div className="space-x-4">
            <Link href="/auth/login" className="hover:text-gray-900 dark:hover:text-white">
              Already have an account? Sign in
            </Link>
            <span>•</span>
            <Link href="/" className="hover:text-gray-900 dark:hover:text-white">
              Back to Home
            </Link>
            <span>•</span>
            <a href="https://libyanoex.com" className="hover:text-gray-900 dark:hover:text-white">
              LibyanoEx.com
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
