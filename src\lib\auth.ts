import { createSupabaseBrowserClient } from './supabase'
import { User } from '@/types'

// Mock user for development when Supabase is not configured
const getMockUser = (): User => ({
  id: 'dev-user-123',
  email: '<EMAIL>',
  full_name: 'Development User',
  avatar_url: null,
  role: 'admin',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  is_active: true,
})

// Client-side auth functions only (removing server-side functions that cause issues)
export async function getUser(): Promise<User | null> {
  // For development, return mock user
  // In production, this should be handled by server components properly
  return getMockUser()
}

export async function signOut() {
  const supabase = createSupabaseBrowserClient()
  if (supabase) {
    await supabase.auth.signOut()
  }
}

// Client-side auth functions
export async function signInWithEmail(email: string, password: string) {
  const supabase = createSupabaseBrowserClient()

  if (!supabase) {
    throw new Error('Supabase is not configured. Please set up your environment variables.')
  }

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (error) {
    throw new Error(error.message)
  }

  return data
}

export async function signUpWithEmail(
  email: string,
  password: string,
  fullName: string
) {
  const supabase = createSupabaseBrowserClient()

  if (!supabase) {
    throw new Error('Supabase is not configured. Please set up your environment variables.')
  }

  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
      },
    },
  })

  if (error) {
    throw new Error(error.message)
  }

  return data
}

export async function resetPassword(email: string) {
  const supabase = createSupabaseBrowserClient()

  if (!supabase) {
    throw new Error('Supabase is not configured. Please set up your environment variables.')
  }

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/auth/reset-password`,
  })

  if (error) {
    throw new Error(error.message)
  }
}

export async function updatePassword(password: string) {
  const supabase = createSupabaseBrowserClient()

  if (!supabase) {
    throw new Error('Supabase is not configured. Please set up your environment variables.')
  }

  const { error } = await supabase.auth.updateUser({
    password,
  })

  if (error) {
    throw new Error(error.message)
  }
}

// Role-based access control
export function hasRole(user: User | null, roles: string[]): boolean {
  if (!user) return false
  return roles.includes(user.role)
}

export function isAdmin(user: User | null): boolean {
  return hasRole(user, ['admin'])
}

export function isManager(user: User | null): boolean {
  return hasRole(user, ['admin', 'manager'])
}

export function canAccessAdminPanel(user: User | null): boolean {
  return hasRole(user, ['admin', 'manager'])
}
